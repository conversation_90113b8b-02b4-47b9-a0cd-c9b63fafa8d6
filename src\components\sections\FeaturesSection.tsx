'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Camera, 
  Brain, 
  Bell, 
  Shield, 
  Smartphone, 
  Home, 
  Car, 
  Utensils,
  Scan,
  FileText,
  Calendar,
  Archive
} from 'lucide-react'
import Card, { CardContent, CardHeader } from '../ui/Card'
import Button from '../ui/Button'

export default function FeaturesSection() {
  const [activeFeature, setActiveFeature] = useState(0)

  const features = [
    {
      icon: Camera,
      title: 'Smart Receipt Scanning',
      description: 'AI-powered OCR extracts warranty information from photos, emails, and documents instantly',
      details: [
        'Scan receipts with your phone camera',
        'Auto-import from Gmail and shopping apps',
        'Extract product details, warranty dates, and serial numbers',
        'Support for 50+ languages and formats'
      ],
      demo: '📱 → 🤖 → ✅',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Brain,
      title: 'AI Product Recognition',
      description: 'Advanced machine learning identifies products, brands, and warranty terms automatically',
      details: [
        'Recognize 10M+ products in our database',
        'Auto-fill warranty periods by brand and model',
        'Smart categorization by product type',
        'Continuous learning from new products'
      ],
      demo: '🔍 → 🧠 → 📋',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Bell,
      title: 'Smart Reminders',
      description: 'Intelligent notifications ensure you never miss warranty expiration or service dates',
      details: [
        'Customizable reminder schedules',
        'Multi-channel notifications (email, SMS, push)',
        'Service maintenance reminders',
        'Claim deadline alerts'
      ],
      demo: '⏰ → 📱 → 🔔',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Shield,
      title: 'Warranty Protection',
      description: 'Comprehensive coverage tracking across all your valuable possessions',
      details: [
        'Track extended warranties and service plans',
        'Monitor coverage limits and deductibles',
        'Claim history and documentation',
        'Insurance integration'
      ],
      demo: '🛡️ → 📊 → 💰',
      color: 'from-orange-500 to-red-500'
    }
  ]

  const categories = [
    { icon: Smartphone, name: 'Electronics', count: '12 items', color: 'text-blue-400' },
    { icon: Home, name: 'Home & Garden', count: '8 items', color: 'text-green-400' },
    { icon: Car, name: 'Vehicles', count: '2 items', color: 'text-orange-400' },
    { icon: Utensils, name: 'Kitchen', count: '6 items', color: 'text-purple-400' }
  ]

  return (
    <section id="features" className="py-20 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="gradient-text">Powerful Features</span>
            <br />
            <span className="text-foreground">Built for You</span>
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto">
            Everything you need to manage warranties, track coverage, and protect your investments
          </p>
        </motion.div>

        {/* Interactive Feature Showcase */}
        <div className="grid lg:grid-cols-2 gap-12 mb-20">
          {/* Feature Tabs */}
          <div className="space-y-4">
            {features.map((feature, index) => {
              const Icon = feature.icon
              const isActive = activeFeature === index
              
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card 
                    className={`p-6 cursor-pointer transition-all duration-300 ${
                      isActive 
                        ? 'border-primary/50 bg-primary/5 glow' 
                        : 'hover:border-primary/30'
                    }`}
                    onClick={() => setActiveFeature(index)}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.color} text-white`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                        <p className="text-foreground/70">{feature.description}</p>
                        {isActive && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="mt-4 space-y-2"
                          >
                            {feature.details.map((detail, i) => (
                              <div key={i} className="flex items-center space-x-2 text-sm">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                                <span className="text-foreground/80">{detail}</span>
                              </div>
                            ))}
                          </motion.div>
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )
            })}
          </div>

          {/* Feature Demo */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="p-8 glass glow relative overflow-hidden">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="text-center"
                >
                  <div className="text-6xl mb-6">{features[activeFeature].demo}</div>
                  <h3 className="text-2xl font-bold gradient-text mb-4">
                    {features[activeFeature].title}
                  </h3>
                  <p className="text-foreground/70 mb-6">
                    {features[activeFeature].description}
                  </p>
                  
                  {/* Interactive Demo Visualization */}
                  <div className="bg-muted/10 rounded-lg p-6 border border-border/20">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <Scan className="h-6 w-6 text-primary" />
                        </div>
                        <span className="text-foreground/60">Input</span>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-secondary/20 to-accent/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <Brain className="h-6 w-6 text-secondary" />
                        </div>
                        <span className="text-foreground/60">AI Process</span>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-accent/20 to-primary/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <FileText className="h-6 w-6 text-accent" />
                        </div>
                        <span className="text-foreground/60">Result</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </Card>
          </motion.div>
        </div>

        {/* Category Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold gradient-text mb-4">Track Everything</h3>
            <p className="text-foreground/70">Organize warranties across all your valuable possessions</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category, index) => {
              const Icon = category.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="p-6 text-center hover:scale-105 transition-transform duration-300">
                    <div className={`inline-flex p-4 rounded-full bg-muted/10 mb-4 ${category.color}`}>
                      <Icon className="h-8 w-8" />
                    </div>
                    <h4 className="text-xl font-semibold mb-2">{category.name}</h4>
                    <p className="text-foreground/60">{category.count}</p>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Button variant="primary" size="lg">
            Try All Features Free
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

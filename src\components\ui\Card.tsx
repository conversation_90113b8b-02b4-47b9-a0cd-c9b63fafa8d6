'use client'

import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'
import { ReactNode } from 'react'

interface CardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  glow?: boolean
  glass?: boolean
}

export default function Card({ 
  children, 
  className, 
  hover = true, 
  glow = false,
  glass = false 
}: CardProps) {
  const baseClasses = 'rounded-xl border border-border/20 transition-all duration-300'
  const glassClasses = glass ? 'glass' : 'bg-muted/10'
  const glowClasses = glow ? 'glow' : ''
  const hoverClasses = hover ? 'hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1' : ''

  return (
    <motion.div
      className={cn(baseClasses, glassClasses, glowClasses, hoverClasses, className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={hover ? { y: -4 } : {}}
    >
      {children}
    </motion.div>
  )
}

export function CardHeader({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn('p-6 pb-4', className)}>
      {children}
    </div>
  )
}

export function CardContent({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn('p-6 pt-0', className)}>
      {children}
    </div>
  )
}

export function CardFooter({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn('p-6 pt-4', className)}>
      {children}
    </div>
  )
}

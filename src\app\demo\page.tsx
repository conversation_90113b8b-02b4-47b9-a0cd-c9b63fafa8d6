'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Upload, 
  Camera, 
  FileText, 
  Scan, 
  CheckCircle, 
  Bell, 
  Calendar,
  DollarSign,
  Shield,
  ArrowLeft,
  Download,
  Share2
} from 'lucide-react'
import Navigation from '@/components/Navigation'
import Button from '@/components/ui/Button'
import Card, { CardContent, CardHeader } from '@/components/ui/Card'
import { sampleWarranties, formatCurrency, formatDate } from '@/lib/utils'

export default function DemoPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [extractedData, setExtractedData] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [warranties, setWarranties] = useState(sampleWarranties)

  const demoSteps = [
    { title: 'Upload Receipt', icon: Upload },
    { title: 'AI Processing', icon: Scan },
    { title: 'Data Extraction', icon: FileText },
    { title: 'Warranty Added', icon: CheckCircle }
  ]

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedFile(file)
      setCurrentStep(1)
      simulateProcessing()
    }
  }

  const simulateProcessing = () => {
    setIsProcessing(true)
    
    // Step 1: AI Processing
    setTimeout(() => {
      setCurrentStep(2)
    }, 2000)

    // Step 2: Data Extraction
    setTimeout(() => {
      const mockData = {
        name: 'AirPods Pro (2nd Gen)',
        brand: 'Apple',
        category: 'Electronics',
        purchaseDate: '2024-11-20',
        warrantyExpiry: '2025-11-20',
        serialNumber: 'MLWK3LL/A',
        price: 249,
        status: 'active',
        daysRemaining: 365,
        image: '🎧'
      }
      setExtractedData(mockData)
      setCurrentStep(3)
      setIsProcessing(false)
    }, 4000)

    // Step 3: Add to warranties
    setTimeout(() => {
      if (extractedData) {
        setWarranties(prev => [{ ...extractedData, id: Date.now().toString() }, ...prev])
      }
    }, 5000)
  }

  const resetDemo = () => {
    setCurrentStep(0)
    setUploadedFile(null)
    setExtractedData(null)
    setIsProcessing(false)
  }

  return (
    <main className="min-h-screen bg-background">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              <span className="gradient-text">Live Demo</span>
              <br />
              <span className="text-foreground">WarrantyAI in Action</span>
            </h1>
            <p className="text-xl text-foreground/80 max-w-3xl mx-auto mb-8">
              Experience how WarrantyAI transforms receipt scanning into organized warranty management
            </p>
            <Button variant="outline" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Demo Interface */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card className="p-8 glass glow">
                <CardHeader>
                  <h2 className="text-2xl font-bold gradient-text mb-4">Upload Receipt Demo</h2>
                  <p className="text-foreground/70">Try uploading a receipt to see AI extraction in action</p>
                </CardHeader>

                <CardContent>
                  {/* Progress Steps */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      {demoSteps.map((step, index) => {
                        const Icon = step.icon
                        const isActive = currentStep === index
                        const isCompleted = currentStep > index
                        
                        return (
                          <div key={index} className="flex flex-col items-center">
                            <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                              isActive ? 'bg-primary text-background scale-110' :
                              isCompleted ? 'bg-green-500 text-background' :
                              'bg-muted/20 text-foreground/60'
                            }`}>
                              <Icon className="h-5 w-5" />
                            </div>
                            <span className={`text-xs text-center ${
                              isActive ? 'text-primary font-semibold' :
                              isCompleted ? 'text-green-400' :
                              'text-foreground/60'
                            }`}>
                              {step.title}
                            </span>
                          </div>
                        )
                      })}
                    </div>
                    <div className="w-full bg-muted/20 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full"
                        initial={{ width: '0%' }}
                        animate={{ width: `${(currentStep / (demoSteps.length - 1)) * 100}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>

                  {/* Upload Area */}
                  {currentStep === 0 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="border-2 border-dashed border-primary/30 rounded-lg p-8 text-center hover:border-primary/50 transition-colors"
                    >
                      <Camera className="h-12 w-12 text-primary mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Upload Receipt</h3>
                      <p className="text-foreground/70 mb-4">
                        Take a photo or upload an image of your receipt
                      </p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="receipt-upload"
                      />
                      <label htmlFor="receipt-upload">
                        <Button variant="primary" className="cursor-pointer">
                          Choose File
                        </Button>
                      </label>
                    </motion.div>
                  )}

                  {/* Processing */}
                  {currentStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-8"
                    >
                      <motion.div
                        className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      <h3 className="text-lg font-semibold mb-2">AI Processing</h3>
                      <p className="text-foreground/70">Analyzing receipt with advanced OCR...</p>
                    </motion.div>
                  )}

                  {/* Data Extraction */}
                  {currentStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-8"
                    >
                      <motion.div
                        className="w-16 h-16 bg-gradient-to-r from-secondary to-accent rounded-full mx-auto mb-4 flex items-center justify-center"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        <Scan className="h-8 w-8 text-white" />
                      </motion.div>
                      <h3 className="text-lg font-semibold mb-2">Extracting Data</h3>
                      <p className="text-foreground/70">Identifying product details and warranty information...</p>
                    </motion.div>
                  )}

                  {/* Results */}
                  {currentStep === 3 && extractedData && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-4"
                    >
                      <div className="bg-gradient-to-r from-green-500/10 to-primary/10 p-6 rounded-lg border border-green-500/30">
                        <div className="flex items-center space-x-4 mb-4">
                          <div className="text-4xl">{extractedData.image}</div>
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-primary">{extractedData.name}</h3>
                            <p className="text-foreground/80">{extractedData.brand}</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-green-400" />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-foreground/60">Purchase Date:</span>
                            <p className="font-semibold">{formatDate(extractedData.purchaseDate)}</p>
                          </div>
                          <div>
                            <span className="text-foreground/60">Warranty Expires:</span>
                            <p className="font-semibold text-green-400">{formatDate(extractedData.warrantyExpiry)}</p>
                          </div>
                          <div>
                            <span className="text-foreground/60">Price:</span>
                            <p className="font-semibold">{formatCurrency(extractedData.price)}</p>
                          </div>
                          <div>
                            <span className="text-foreground/60">Serial Number:</span>
                            <p className="font-semibold">{extractedData.serialNumber}</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-4">
                        <Button variant="primary" className="flex-1">
                          <Shield className="h-4 w-4 mr-2" />
                          Add to Warranties
                        </Button>
                        <Button variant="outline" onClick={resetDemo}>
                          Try Again
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Warranty Dashboard */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card className="p-8 glass">
                <CardHeader>
                  <h2 className="text-2xl font-bold gradient-text mb-4">Your Warranties</h2>
                  <p className="text-foreground/70">Live dashboard showing all tracked warranties</p>
                </CardHeader>

                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {warranties.slice(0, 5).map((warranty, index) => (
                      <motion.div
                        key={warranty.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className={`p-4 rounded-lg border transition-all duration-300 ${
                          warranty.status === 'active' ? 'border-green-500/30 bg-green-500/5' :
                          warranty.status === 'expiring' ? 'border-orange-500/30 bg-orange-500/5' :
                          'border-red-500/30 bg-red-500/5'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-2xl">{warranty.image || '📦'}</div>
                            <div>
                              <h4 className="font-semibold">{warranty.name}</h4>
                              <p className="text-sm text-foreground/60">{warranty.brand}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm font-semibold ${
                              warranty.status === 'active' ? 'text-green-400' :
                              warranty.status === 'expiring' ? 'text-orange-400' :
                              'text-red-400'
                            }`}>
                              {warranty.daysRemaining > 0 ? `${warranty.daysRemaining} days left` : 'Expired'}
                            </div>
                            <p className="text-xs text-foreground/60">
                              {formatDate(warranty.warrantyExpiry)}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  <div className="mt-6 pt-6 border-t border-border/20">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-green-400">{warranties.filter(w => w.status === 'active').length}</div>
                        <div className="text-xs text-foreground/60">Active</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-orange-400">{warranties.filter(w => w.status === 'expiring').length}</div>
                        <div className="text-xs text-foreground/60">Expiring</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-red-400">{warranties.filter(w => w.status === 'expired').length}</div>
                        <div className="text-xs text-foreground/60">Expired</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Demo Features */}
          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <h3 className="text-2xl font-bold gradient-text mb-8">What You Just Experienced</h3>
            
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="p-6">
                <Scan className="h-12 w-12 text-primary mx-auto mb-4" />
                <h4 className="text-lg font-semibold mb-2">AI-Powered OCR</h4>
                <p className="text-foreground/70 text-sm">
                  Advanced machine learning extracts text and data from any receipt format
                </p>
              </Card>
              
              <Card className="p-6">
                <FileText className="h-12 w-12 text-secondary mx-auto mb-4" />
                <h4 className="text-lg font-semibold mb-2">Smart Data Extraction</h4>
                <p className="text-foreground/70 text-sm">
                  Automatically identifies products, warranties, and important dates
                </p>
              </Card>
              
              <Card className="p-6">
                <Bell className="h-12 w-12 text-accent mx-auto mb-4" />
                <h4 className="text-lg font-semibold mb-2">Intelligent Reminders</h4>
                <p className="text-foreground/70 text-sm">
                  Never miss warranty expiration with smart notification system
                </p>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>
    </main>
  )
}

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Warranty data types
export interface WarrantyItem {
  id: string
  name: string
  brand: string
  category: string
  purchaseDate: string
  warrantyExpiry: string
  serialNumber?: string
  price?: number
  receipt?: string
  status: 'active' | 'expiring' | 'expired'
  daysRemaining: number
  image?: string
}

export interface WarrantyCategory {
  id: string
  name: string
  icon: string
  count: number
  color: string
}

// Sample warranty data for demo
export const sampleWarranties: WarrantyItem[] = [
  {
    id: '1',
    name: 'MacBook Pro 16"',
    brand: 'Apple',
    category: 'Electronics',
    purchaseDate: '2024-01-15',
    warrantyExpiry: '2025-01-15',
    serialNumber: 'C02XK0XKMD6T',
    price: 2499,
    status: 'active',
    daysRemaining: 45,
    image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400'
  },
  {
    id: '2',
    name: 'Samsung 4K TV',
    brand: 'Samsung',
    category: 'Electronics',
    purchaseDate: '2023-12-01',
    warrantyExpiry: '2024-12-01',
    serialNumber: 'UN55TU8000FXZA',
    price: 899,
    status: 'expiring',
    daysRemaining: 15,
    image: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400'
  },
  {
    id: '3',
    name: 'Dyson V15 Vacuum',
    brand: 'Dyson',
    category: 'Home Appliances',
    purchaseDate: '2023-06-15',
    warrantyExpiry: '2024-06-15',
    serialNumber: 'DY-V15-2023',
    price: 649,
    status: 'expired',
    daysRemaining: -30,
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
  },
  {
    id: '4',
    name: 'iPhone 15 Pro',
    brand: 'Apple',
    category: 'Electronics',
    purchaseDate: '2024-09-22',
    warrantyExpiry: '2025-09-22',
    serialNumber: 'F2LX3LL/A',
    price: 999,
    status: 'active',
    daysRemaining: 300,
    image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400'
  },
  {
    id: '5',
    name: 'Tesla Model 3',
    brand: 'Tesla',
    category: 'Vehicle',
    purchaseDate: '2023-03-10',
    warrantyExpiry: '2027-03-10',
    serialNumber: 'TM3-2023-001',
    price: 45000,
    status: 'active',
    daysRemaining: 1200,
    image: 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400'
  }
]

export const warrantyCategories: WarrantyCategory[] = [
  { id: '1', name: 'Electronics', icon: '📱', count: 12, color: '#00d4ff' },
  { id: '2', name: 'Home Appliances', icon: '🏠', count: 8, color: '#7c3aed' },
  { id: '3', name: 'Vehicle', icon: '🚗', count: 2, color: '#f59e0b' },
  { id: '4', name: 'Kitchen', icon: '🍳', count: 6, color: '#10b981' },
  { id: '5', name: 'Garden', icon: '🌱', count: 4, color: '#ef4444' },
  { id: '6', name: 'Tools', icon: '🔧', count: 5, color: '#8b5cf6' }
]

// Utility functions
export const calculateDaysRemaining = (expiryDate: string): number => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

export const getWarrantyStatus = (daysRemaining: number): 'active' | 'expiring' | 'expired' => {
  if (daysRemaining < 0) return 'expired'
  if (daysRemaining <= 30) return 'expiring'
  return 'active'
}

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(key, JSON.stringify(data))
  }
}

export const getFromLocalStorage = (key: string) => {
  if (typeof window !== 'undefined') {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : null
  }
  return null
}

// Animation utilities
export const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

export const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.5, ease: [0.6, -0.05, 0.01, 0.99] }
}

import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "WarrantyAI - Never Miss a Warranty Again",
  description: "Smart AI assistant to track, manage, and remind users of all warranties, services, and coverage across electronics, home, vehicles, and appliances.",
  keywords: "warranty tracking, AI assistant, receipt scanner, warranty management, smart reminders",
  authors: [{ name: "WarrantyAI Team" }],
  creator: "WarrantyAI",
  publisher: "WarrantyAI",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://warrantyai.com",
    title: "WarrantyAI - Never Miss a Warranty Again",
    description: "Smart AI assistant to track, manage, and remind users of all warranties, services, and coverage.",
    siteName: "WarrantyAI",
  },
  twitter: {
    card: "summary_large_image",
    title: "WarrantyAI - Never Miss a Warranty Again",
    description: "Smart AI assistant to track, manage, and remind users of all warranties, services, and coverage.",
    creator: "@warrantyai",
  },
  icons: {
    icon: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    shortcut: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    apple: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

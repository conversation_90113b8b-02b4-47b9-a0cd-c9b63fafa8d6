'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, Scan, Bell, CheckCircle, Camera, FileText, Smartphone } from 'lucide-react'
import But<PERSON> from '../ui/Button'
import Card from '../ui/Card'

export default function HeroSection() {
  const [currentStep, setCurrentStep] = useState(0)
  const [isScanning, setIsScanning] = useState(false)
  const [extractedData, setExtractedData] = useState(null)

  const demoSteps = [
    {
      title: 'Upload Receipt',
      icon: Upload,
      description: 'Take a photo or upload your receipt',
      action: 'upload'
    },
    {
      title: 'AI Scanning',
      icon: Scan,
      description: 'AI extracts warranty information',
      action: 'scan'
    },
    {
      title: 'Smart Reminders',
      icon: Bell,
      description: 'Get notified before expiry',
      action: 'remind'
    },
    {
      title: 'Protected',
      icon: CheckCircle,
      description: 'Never miss a warranty again',
      action: 'complete'
    }
  ]

  const sampleProducts = [
    {
      name: 'MacBook Pro 16"',
      brand: 'Apple',
      warranty: '365 days',
      price: '$2,499',
      image: '💻'
    },
    {
      name: 'iPhone 15 Pro',
      brand: 'Apple', 
      warranty: '365 days',
      price: '$999',
      image: '📱'
    },
    {
      name: 'Samsung TV',
      brand: 'Samsung',
      warranty: '730 days',
      price: '$899',
      image: '📺'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      if (currentStep === 1) {
        setIsScanning(true)
        setTimeout(() => {
          setIsScanning(false)
          setExtractedData(sampleProducts[Math.floor(Math.random() * sampleProducts.length)])
          setCurrentStep(2)
        }, 2000)
      } else if (currentStep === 3) {
        setTimeout(() => {
          setCurrentStep(0)
          setExtractedData(null)
        }, 3000)
      } else {
        setCurrentStep((prev) => (prev + 1) % 4)
      }
    }, 4000)

    return () => clearInterval(interval)
  }, [currentStep])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Multi-layer Background */}
      <div className="absolute inset-0">
        {/* Layer 1: Base gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background" />
        
        {/* Layer 2: Animated particles */}
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Layer 3: Geometric patterns */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 border border-primary/20 rounded-full animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 border border-secondary/20 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
        </div>

        {/* Layer 4: Gradient orbs */}
        <motion.div
          className="absolute top-1/3 left-1/5 w-96 h-96 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/5 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <motion.div
            className="text-center lg:text-left"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.h1
              className="text-5xl lg:text-7xl font-bold mb-6 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="gradient-text">Never Miss</span>
              <br />
              <span className="text-foreground">a Warranty</span>
              <br />
              <span className="gradient-text">Again</span>
            </motion.h1>

            <motion.p
              className="text-xl text-foreground/80 mb-8 max-w-2xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Smart AI assistant that tracks, manages, and reminds you of all warranties, 
              services, and coverage across electronics, home, vehicles, and appliances.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Button variant="primary" size="lg" className="group">
                Start Free Trial
                <motion.div
                  className="ml-2 group-hover:translate-x-1 transition-transform"
                  initial={{ x: 0 }}
                  whileHover={{ x: 4 }}
                >
                  →
                </motion.div>
              </Button>
              <Button variant="outline" size="lg">
                Watch Demo
              </Button>
            </motion.div>

            <motion.div
              className="mt-8 flex items-center justify-center lg:justify-start space-x-6 text-sm text-foreground/60"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Free 30-day trial</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>No credit card required</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Interactive Demo */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Card className="p-8 glass glow relative overflow-hidden">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold gradient-text mb-2">Live Demo</h3>
                <p className="text-foreground/60">Watch WarrantyAI in action</p>
              </div>

              {/* Demo Steps */}
              <div className="space-y-4 mb-8">
                {demoSteps.map((step, index) => {
                  const Icon = step.icon
                  const isActive = currentStep === index
                  const isCompleted = currentStep > index

                  return (
                    <motion.div
                      key={index}
                      className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-500 ${
                        isActive ? 'bg-primary/10 border border-primary/30' : 
                        isCompleted ? 'bg-green-500/10 border border-green-500/30' : 
                        'bg-muted/5 border border-border/20'
                      }`}
                      animate={{
                        scale: isActive ? 1.02 : 1,
                        opacity: isActive ? 1 : 0.7
                      }}
                    >
                      <div className={`p-2 rounded-full ${
                        isActive ? 'bg-primary text-background' :
                        isCompleted ? 'bg-green-500 text-background' :
                        'bg-muted/20 text-foreground/60'
                      }`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{step.title}</h4>
                        <p className="text-sm text-foreground/60">{step.description}</p>
                      </div>
                      {isActive && currentStep === 1 && isScanning && (
                        <motion.div
                          className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                      )}
                    </motion.div>
                  )
                })}
              </div>

              {/* Extracted Data Display */}
              <AnimatePresence>
                {extractedData && (
                  <motion.div
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.9 }}
                    className="bg-gradient-to-r from-primary/10 to-secondary/10 p-4 rounded-lg border border-primary/30"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-4xl">{extractedData.image}</div>
                      <div className="flex-1">
                        <h4 className="font-bold text-primary">{extractedData.name}</h4>
                        <p className="text-sm text-foreground/80">{extractedData.brand}</p>
                        <div className="flex justify-between mt-2 text-sm">
                          <span className="text-green-400">Warranty: {extractedData.warranty}</span>
                          <span className="text-accent font-semibold">{extractedData.price}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

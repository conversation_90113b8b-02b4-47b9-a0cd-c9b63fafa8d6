'use client'

import { motion } from 'framer-motion'
import { Alert<PERSON>riangle, CheckCircle, ArrowRight, Receipt, Clock, Shield } from 'lucide-react'
import Card from '../ui/Card'

export default function ProblemSolutionSection() {
  const problems = [
    {
      icon: Receipt,
      title: 'Lost Receipts',
      description: '70% of consumers lose or misplace receipts within 6 months',
      color: 'text-red-400'
    },
    {
      icon: Clock,
      title: 'Missed Deadlines',
      description: 'Warranty claims expire unnoticed, losing thousands in coverage',
      color: 'text-orange-400'
    },
    {
      icon: AlertTriangle,
      title: 'Manual Tracking',
      description: 'Chaotic spreadsheets and forgotten service dates',
      color: 'text-yellow-400'
    }
  ]

  const solutions = [
    {
      icon: Shield,
      title: 'AI-Powered Protection',
      description: 'Smart scanning extracts warranty info automatically',
      color: 'text-primary'
    },
    {
      icon: CheckCircle,
      title: 'Smart Reminders',
      description: 'Never miss expiry dates with intelligent notifications',
      color: 'text-green-400'
    },
    {
      icon: Receipt,
      title: 'Digital Organization',
      description: 'All warranties centralized in one secure location',
      color: 'text-blue-400'
    }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-red-500/5 to-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-red-400">The Problem</span>
            <span className="mx-4">→</span>
            <span className="gradient-text">Our Solution</span>
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto">
            Transform warranty chaos into organized protection with AI-powered automation
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Problems Side */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center lg:text-left mb-8">
              <h3 className="text-3xl font-bold text-red-400 mb-4">Current Problems</h3>
              <p className="text-foreground/70">Why warranty management is broken today</p>
            </div>

            <div className="space-y-6">
              {problems.map((problem, index) => {
                const Icon = problem.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  >
                    <Card className="p-6 border-red-500/20 hover:border-red-500/40">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg bg-red-500/10 ${problem.color}`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-xl font-semibold mb-2">{problem.title}</h4>
                          <p className="text-foreground/70">{problem.description}</p>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>

          {/* Arrow Transition */}
          <motion.div
            className="hidden lg:flex justify-center"
            initial={{ opacity: 0, scale: 0.5 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              <motion.div
                className="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
              >
                <ArrowRight className="h-8 w-8 text-white" />
              </motion.div>
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-lg"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
          </motion.div>

          {/* Solutions Side */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center lg:text-left mb-8">
              <h3 className="text-3xl font-bold gradient-text mb-4">WarrantyAI Solution</h3>
              <p className="text-foreground/70">How we solve warranty management forever</p>
            </div>

            <div className="space-y-6">
              {solutions.map((solution, index) => {
                const Icon = solution.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  >
                    <Card className="p-6 border-primary/20 hover:border-primary/40 glow-hover">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg bg-primary/10 ${solution.color}`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-xl font-semibold mb-2">{solution.title}</h4>
                          <p className="text-foreground/70">{solution.description}</p>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-2xl border border-primary/20">
            <div className="text-4xl">🎯</div>
            <div className="text-left">
              <h4 className="text-xl font-bold gradient-text">Ready to solve this?</h4>
              <p className="text-foreground/70">Join thousands protecting their warranties with AI</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
